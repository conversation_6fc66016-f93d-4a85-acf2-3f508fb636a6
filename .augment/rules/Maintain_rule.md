---
type: "always_apply"
---

# 🛠️ AUGMENT DEVELOPMENT RULES

> ✅ Follow these rules strictly for clean, consistent, and scalable development.

---

## 📁 Project Structure & File Management

1. **Documentation**
   - Always create new documentation files inside the `/docs` directory.
   - Never place documentation directly in the root folder.

2. **Script and File Organization**
   - Maintain the appropriate directory structure (e.g., `scripts/`, `utils/`, `jobs/`, `api/`, `components/`).
   - Do **not** add implementation files at the root level.

3. **Redundancy Elimination**
   - Remove test, boilerplate, or sample scripts after final implementations are complete.
   - Eliminate any duplicate or redundant files serving the same functionality.

---

## 🔁 Development Workflow

4. **Implementation Flow**
   - Always begin development **from backend to frontend**.
   - Set up APIs, schemas, database connections, and core logic first.
   - Proceed with frontend only after backend foundation is in place.

5. **Schema Consistency**
   - Ensure **field names in APIs** match the schema/database.
   - Maintain consistent naming conventions (e.g., `camelCase`, `snake_case`) across the stack.

6. **Module Communication**
   - Ensure **proper integration and communication** between:
     - Backend ↔ Database
     - Frontend ↔ Backend
     - Reusable modules ↔ Core services
   - Handle API responses and error codes uniformly across services.

---

## 🧼 Error-Free & Clean Code

7. **Type & Syntax Safety**
   - Fix all **TypeScript errors**, **syntax issues**, and **import errors** before commit.
   - Code must be fully functional, error-free, and pass linter checks.

---

## 🎨 Consistency & Standards

8. **Styling Consistency**
   - Maintain **consistent styling** across all UI components (color palette, spacing, typography, UI patterns).
   - Follow the project's design system or use a utility-first CSS framework like **TailwindCSS**.

9. **Code Quality & Best Practices**
   - Write **modular, reusable, and documented** code.
   - Follow **industry-standard conventions** (e.g., RESTful APIs, MVC pattern, secure coding).
   - Use `.env` for sensitive values—never hardcode credentials.

---

## ✅ Feature Completion Checklist

- [ ] Docs created under `/docs`
- [ ] No files at root that belong elsewhere
- [ ] All temp/test scripts removed
- [ ] Backend > DB > Frontend flow followed
- [ ] API fields match schema
- [ ] All TypeScript, syntax, import errors fixed
- [ ] Styling and component design is consistent
- [ ] All modules properly integrated and communicating
- [ ] Industry standards maintained throughout

---